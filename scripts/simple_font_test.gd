extends Node

# Jednoduchý test fontov - spustí sa pri načítaní

func _ready():
	print("=== TESTOVANIE FONTOV ===")
	test_font_creation()

func test_font_creation():
	print("Testovanie vytvárania fontov...")

	# Test FontLoader
	var font_loader = preload("res://scripts/font_loader.gd")

	# Test vytvorenia jednoduchého fontu
	print("Testovanie chapter_title fontu...")
	var chapter_font = font_loader.create_font_variation("chapter_title")
	print("✅ Chapter font vytvorený: ", chapter_font != null)

	if chapter_font:
		print("Chapter font names: ", chapter_font.font_names)
		print("Chapter font type: ", typeof(chapter_font))
		print("Chapter font class: ", chapter_font.get_class())

	# Test ďalších fontov postupne
	print("Testovanie character_dialogue fontu...")
	var dialogue_font = font_loader.create_font_variation("character_dialogue")
	print("✅ Dialogue font vytvorený: ", dialogue_font != null)

	print("Testovanie narrator_text fontu...")
	var narrator_font = font_loader.create_font_variation("narrator_text")
	print("✅ Narrator font vytvorený: ", narrator_font != null)

	print("Testovanie ui_elements fontu...")
	var ui_font = font_loader.create_font_variation("ui_elements")
	print("✅ UI font vytvorený: ", ui_font != null)

	print("Testovanie puzzle_text fontu...")
	var puzzle_font = font_loader.create_font_variation("puzzle_text")
	print("✅ Puzzle font vytvorený: ", puzzle_font != null)

	print("=== TEST DOKONČENÝ ===")

	# Ukončiť po 5 sekundách
	await get_tree().create_timer(5.0).timeout
	print("Test ukončený.")
	get_tree().quit()
