extends Control
class_name Chapter

@export var chapter_number: int = 1
@onready var dialogue_system: DialogueSystem = $DialogueSystem
@onready var chapter_title: Label = $VBoxContainer/ChapterTitle
@onready var puzzle1_button: Button = $VBoxContainer/PuzzlesContainer/Puzzle1Button
@onready var puzzle2_button: Button = $VBoxContainer/PuzzlesContainer/Puzzle2Button
@onready var back_button: Button = $VBoxContainer/BackButton
@onready var hint_button: Button = $VBoxContainer/HintButton
@onready var start_button: Button = $VBoxContainer/StartButton
@onready var background: NinePatchRect = $Background

# Import font utilities
const ApplyChapterFonts = preload("res://scripts/apply_chapter_fonts.gd")

var current_puzzle: int = 0
var puzzles_completed: Array[bool] = [false, false]
var story_phase: int = 0  # 0=úvod, 1=po prvom hlavolame, 2=po druhom hlavolame
var waiting_for_puzzle: bool = false

func _ready():
	print("=== KAPITOLA ", chapter_number, " SA NAČÍTAVA ===")
	# Nastavenie textu
	var chapter_info = GameManager.chapter_info[chapter_number]
	print("Info o kapitole: ", chapter_info)
	if chapter_title:
		chapter_title.text = chapter_info.title
		# Aplikovanie gotického fontu na titulok
		ApplyChapterFonts.apply_chapter_title_font(chapter_title)

	# Spustenie hudby pre kapitolu
	start_chapter_music()

	# Pre kapitolu 7 (Epilóg) - skryť hlavolamy a ukázať start button
	if chapter_number == 7:
		if puzzle1_button:
			puzzle1_button.visible = false
		if puzzle2_button:
			puzzle2_button.visible = false
		if hint_button:
			hint_button.visible = false
		if start_button:
			start_button.visible = true
	else:
		# Pre ostatné kapitoly - normálne nastavenie
		if start_button:
			start_button.visible = false
		if chapter_info.puzzles.size() >= 2:
			if puzzle1_button:
				puzzle1_button.text = chapter_info.puzzles[0]
			if puzzle2_button:
				puzzle2_button.text = chapter_info.puzzles[1]

	# Pripojenie signálov - bezpečne
	if puzzle1_button:
		puzzle1_button.pressed.connect(_on_puzzle1_pressed)
	if puzzle2_button:
		puzzle2_button.pressed.connect(_on_puzzle2_pressed)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if start_button:
		start_button.pressed.connect(_on_start_pressed)

	if dialogue_system:
		dialogue_system.dialogue_finished.connect(_on_dialogue_finished)

	# Kontrola stavu hlavolamov
	update_puzzle_buttons()

	# Skrytie tlačidiel hlavolamov na začiatku
	hide_puzzle_buttons()

	# Spustenie úvodného dialógu
	start_chapter_intro()

	# Nastavenie fokusu na back button
	if back_button:
		back_button.grab_focus()

func start_chapter_intro():
	print("Spúšťam úvodné dialógy pre kapitolu ", chapter_number)
	if dialogue_system:
		var intro_dialogue = dialogue_system.get_chapter_intro_dialogue(chapter_number)
		print("Počet úvodných dialógov: ", intro_dialogue.size())
		if intro_dialogue.size() > 0:
			print("Prvý dialóg: ", intro_dialogue[0])
		dialogue_system.start_dialogue(intro_dialogue)
	else:
		print("CHYBA: DialogueSystem nie je dostupný!")

func change_background_image(image_path: String):
	if background:
		var new_texture = load(image_path)
		if new_texture:
			background.texture = new_texture
			print("Pozadie zmenené na: ", image_path)
		else:
			print("CHYBA: Nemožno načítať obrázok: ", image_path)

func hide_puzzle_buttons():
	if puzzle1_button:
		puzzle1_button.visible = false
	if puzzle2_button:
		puzzle2_button.visible = false

func show_puzzle_button(puzzle_number: int):
	if puzzle_number == 1 and puzzle1_button:
		puzzle1_button.visible = true
		puzzle1_button.grab_focus()
	elif puzzle_number == 2 and puzzle2_button:
		puzzle2_button.visible = true
		puzzle2_button.grab_focus()

func update_puzzle_buttons():
	# Aktualizovanie stavu tlačidiel na základe dokončených hlavolamov
	puzzles_completed[0] = GameManager.is_puzzle_completed(chapter_number, 1)
	puzzles_completed[1] = GameManager.is_puzzle_completed(chapter_number, 2)

	if puzzles_completed[0] and puzzle1_button:
		if not puzzle1_button.text.ends_with(" ✓"):
			puzzle1_button.text += " ✓"
		puzzle1_button.modulate = Color.GREEN

	if puzzles_completed[1] and puzzle2_button:
		if not puzzle2_button.text.ends_with(" ✓"):
			puzzle2_button.text += " ✓"
		puzzle2_button.modulate = Color.GREEN

func _on_puzzle1_pressed():
	current_puzzle = 1
	if chapter_number == 1 or chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6:
		# Pre kapitoly 1, 2, 3, 4, 5 a 6 - priamo spustiť hlavolam
		show_puzzle_scene(1)
	else:
		start_puzzle(1)

func _on_puzzle2_pressed():
	current_puzzle = 2
	if chapter_number == 1 or chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6:
		# Pre kapitoly 1, 2, 3, 4, 5 a 6 - priamo spustiť hlavolam
		show_puzzle_scene(2)
	else:
		start_puzzle(2)

func start_puzzle(puzzle_number: int):
	# Spustenie dialógu pre hlavolam (pre ostatné kapitoly)
	if dialogue_system:
		var puzzle_dialogue = dialogue_system.get_puzzle_intro_dialogue(chapter_number, puzzle_number)
		dialogue_system.start_dialogue(puzzle_dialogue)

func _on_dialogue_finished():
	print("Dialógy skončili. Story phase: ", story_phase, ", Chapter: ", chapter_number)
	if chapter_number == 7:
		# Pre epilóg - po dialógoch ukončiť hru
		print("Epilóg dokončený - označiť ako dokončený a návrat do hlavného menu")
		GameManager.complete_epilogue()
		await get_tree().create_timer(3.0).timeout
		GameManager.go_to_main_menu()
	elif chapter_number == 1 or chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6:
		if story_phase == 0:
			# Po úvodných dialógoch - ukázať prvý hlavolam
			print("Zobrazujem prvý hlavolam")
			story_phase = 1
			# Pre Kapitolu 1 - zmeniť pozadie na druhý obrázok
			if chapter_number == 1:
				change_background_image("res://assets/Obrázky/Kapitola_1/2.png")
			show_puzzle_button(1)
		elif story_phase == 2:
			# Po dialógoch medzi hlavolamami - ukázať druhý hlavolam
			print("Zobrazujem druhý hlavolam")
			story_phase = 3
			show_puzzle_button(2)
	elif current_puzzle > 0:
		# Pre ostatné kapitoly - spustenie hlavolamu
		show_puzzle_scene(current_puzzle)

func show_puzzle_scene(puzzle_number: int):
	# Spustenie puzzle hudby
	AudioManager.start_puzzle()

	if chapter_number == 1:
		if puzzle_number == 1:
			show_caesar_cipher_puzzle()
		elif puzzle_number == 2:
			show_navigation_puzzle()
	elif chapter_number == 2:
		if puzzle_number == 1:
			show_blood_inscription_puzzle()
		elif puzzle_number == 2:
			show_order_test_puzzle()
	elif chapter_number == 3:
		if puzzle_number == 1:
			show_reversed_message_puzzle()
		elif puzzle_number == 2:
			show_simple_calculation_puzzle()
	elif chapter_number == 4:
		if puzzle_number == 1:
			show_memory_test_puzzle()
		elif puzzle_number == 2:
			show_vampire_arithmetic_puzzle()
	elif chapter_number == 5:
		if puzzle_number == 1:
			show_shadow_code_puzzle()
		elif puzzle_number == 2:
			show_three_levers_puzzle()
	elif chapter_number == 6:
		if puzzle_number == 1:
			show_three_sisters_puzzle()
		elif puzzle_number == 2:
			show_ritual_rhythm_puzzle()
	else:
		# Pre ostatné kapitoly zatiaľ simulácia
		show_simple_puzzle(puzzle_number)

func show_caesar_cipher_puzzle():
	var caesar_scene_resource = load("res://scenes/CaesarCipherPuzzle.tscn")
	if caesar_scene_resource:
		var caesar_scene = caesar_scene_resource.instantiate()
		add_child(caesar_scene)
		caesar_scene.puzzle_solved.connect(_on_caesar_solved)
		caesar_scene.puzzle_failed.connect(_on_puzzle_failed)
		caesar_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať CaesarCipherPuzzle.tscn")
		show_simple_puzzle(1)

func show_navigation_puzzle():
	var navigation_scene_resource = load("res://scenes/NavigationPuzzle.tscn")
	if navigation_scene_resource:
		var navigation_scene = navigation_scene_resource.instantiate()
		add_child(navigation_scene)
		navigation_scene.puzzle_solved.connect(_on_navigation_solved)
		navigation_scene.puzzle_failed.connect(_on_puzzle_failed)
		navigation_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať NavigationPuzzle.tscn")
		show_simple_puzzle(2)

func show_blood_inscription_puzzle():
	var blood_scene_resource = load("res://scenes/BloodInscriptionPuzzle.tscn")
	if blood_scene_resource:
		var blood_scene = blood_scene_resource.instantiate()
		add_child(blood_scene)
		blood_scene.puzzle_solved.connect(_on_blood_inscription_solved)
		blood_scene.puzzle_failed.connect(_on_puzzle_failed)
		blood_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať BloodInscriptionPuzzle.tscn")
		show_simple_puzzle(1)

func show_order_test_puzzle():
	var order_scene_resource = load("res://scenes/OrderTestPuzzle.tscn")
	if order_scene_resource:
		var order_scene = order_scene_resource.instantiate()
		add_child(order_scene)
		order_scene.puzzle_solved.connect(_on_order_test_solved)
		order_scene.puzzle_failed.connect(_on_puzzle_failed)
		order_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať OrderTestPuzzle.tscn")
		show_simple_puzzle(2)

func show_reversed_message_puzzle():
	var reversed_scene_resource = load("res://scenes/ReversedMessagePuzzle.tscn")
	if reversed_scene_resource:
		var reversed_scene = reversed_scene_resource.instantiate()
		add_child(reversed_scene)
		reversed_scene.puzzle_solved.connect(_on_reversed_message_solved)
		reversed_scene.puzzle_failed.connect(_on_puzzle_failed)
		reversed_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ReversedMessagePuzzle.tscn")
		show_simple_puzzle(1)

func show_simple_calculation_puzzle():
	var calculation_scene_resource = load("res://scenes/SimpleCalculationPuzzle.tscn")
	if calculation_scene_resource:
		var calculation_scene = calculation_scene_resource.instantiate()
		add_child(calculation_scene)
		calculation_scene.puzzle_solved.connect(_on_simple_calculation_solved)
		calculation_scene.puzzle_failed.connect(_on_puzzle_failed)
		calculation_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať SimpleCalculationPuzzle.tscn")
		show_simple_puzzle(2)

func show_memory_test_puzzle():
	var memory_scene_resource = load("res://scenes/MemoryTestPuzzle.tscn")
	if memory_scene_resource:
		var memory_scene = memory_scene_resource.instantiate()
		add_child(memory_scene)
		memory_scene.puzzle_solved.connect(_on_memory_test_solved)
		memory_scene.puzzle_failed.connect(_on_puzzle_failed)
		memory_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať MemoryTestPuzzle.tscn")
		show_simple_puzzle(1)

func show_vampire_arithmetic_puzzle():
	var vampire_scene_resource = load("res://scenes/VampireArithmeticPuzzle.tscn")
	if vampire_scene_resource:
		var vampire_scene = vampire_scene_resource.instantiate()
		add_child(vampire_scene)
		vampire_scene.puzzle_solved.connect(_on_vampire_arithmetic_solved)
		vampire_scene.puzzle_failed.connect(_on_puzzle_failed)
		vampire_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať VampireArithmeticPuzzle.tscn")
		show_simple_puzzle(2)

func show_shadow_code_puzzle():
	var shadow_scene_resource = load("res://scenes/ShadowCodePuzzle.tscn")
	if shadow_scene_resource:
		var shadow_scene = shadow_scene_resource.instantiate()
		add_child(shadow_scene)
		shadow_scene.puzzle_solved.connect(_on_shadow_code_solved)
		shadow_scene.puzzle_failed.connect(_on_puzzle_failed)
		shadow_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ShadowCodePuzzle.tscn")
		show_simple_puzzle(1)

func show_three_levers_puzzle():
	var levers_scene_resource = load("res://scenes/ThreeLeverssPuzzle.tscn")
	if levers_scene_resource:
		var levers_scene = levers_scene_resource.instantiate()
		add_child(levers_scene)
		levers_scene.puzzle_solved.connect(_on_three_levers_solved)
		levers_scene.puzzle_failed.connect(_on_puzzle_failed)
		levers_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ThreeLeverssPuzzle.tscn")
		show_simple_puzzle(2)

func show_three_sisters_puzzle():
	var sisters_scene_resource = load("res://scenes/ThreeSistersPuzzle.tscn")
	if sisters_scene_resource:
		var sisters_scene = sisters_scene_resource.instantiate()
		add_child(sisters_scene)
		sisters_scene.puzzle_solved.connect(_on_three_sisters_solved)
		sisters_scene.puzzle_failed.connect(_on_puzzle_failed)
		sisters_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať ThreeSistersPuzzle.tscn")
		show_simple_puzzle(1)

func show_ritual_rhythm_puzzle():
	var ritual_scene_resource = load("res://scenes/RitualRhythmPuzzle.tscn")
	if ritual_scene_resource:
		var ritual_scene = ritual_scene_resource.instantiate()
		add_child(ritual_scene)
		ritual_scene.puzzle_solved.connect(_on_ritual_rhythm_solved)
		ritual_scene.puzzle_failed.connect(_on_puzzle_failed)
		ritual_scene.show_puzzle()
	else:
		print("Chyba: Nemožno načítať RitualRhythmPuzzle.tscn")
		show_simple_puzzle(2)

func _on_caesar_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is CaesarCipherPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	AudioManager.return_from_puzzle()

	# Pre kapitolu 1 - pokračovať v príbehu
	if chapter_number == 1:
		story_phase = 2
		hide_puzzle_buttons()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_navigation_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is NavigationPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	AudioManager.return_from_puzzle()

	# Pre kapitolu 1 - dokončiť kapitolu
	if chapter_number == 1:
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_blood_inscription_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is BloodInscriptionPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 2 - pokračovať v príbehu
	if chapter_number == 2:
		story_phase = 2
		hide_puzzle_buttons()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_order_test_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is OrderTestPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 2 - dokončiť kapitolu
	if chapter_number == 2:
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_reversed_message_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ReversedMessagePuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 3 - pokračovať v príbehu
	if chapter_number == 3:
		story_phase = 2
		hide_puzzle_buttons()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_simple_calculation_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is SimpleCalculationPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 3 - dokončiť kapitolu
	if chapter_number == 3:
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_memory_test_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is MemoryTestPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 4 - pokračovať v príbehu
	if chapter_number == 4:
		story_phase = 2
		hide_puzzle_buttons()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_vampire_arithmetic_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is VampireArithmeticPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 4 - dokončiť kapitolu
	if chapter_number == 4:
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_shadow_code_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ShadowCodePuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 5 - pokračovať v príbehu
	if chapter_number == 5:
		story_phase = 2
		hide_puzzle_buttons()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_three_levers_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ThreeLeverssPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 5 - dokončiť kapitolu
	if chapter_number == 5:
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_three_sisters_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is ThreeSistersPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 6 - pokračovať v príbehu
	if chapter_number == 6:
		story_phase = 2
		hide_puzzle_buttons()
		if dialogue_system:
			var interlude_dialogue = dialogue_system.get_interlude_dialogue(chapter_number, 1)
			dialogue_system.start_dialogue(interlude_dialogue)
	else:
		complete_puzzle(1)

func _on_ritual_rhythm_solved():
	# Odstránenie puzzle scény
	for child in get_children():
		if child is RitualRhythmPuzzle:
			child.queue_free()

	# Vrátiť sa k predchádzajúcej hudbe
	return_from_puzzle()

	# Pre kapitolu 6 - dokončiť hru
	if chapter_number == 6:
		complete_puzzle(2)
	else:
		complete_puzzle(2)

func _on_puzzle_failed():
	current_puzzle = 0
	# Odstránenie puzzle scén
	for child in get_children():
		if child is CaesarCipherPuzzle or child is NavigationPuzzle or child is BloodInscriptionPuzzle or child is OrderTestPuzzle or child is ReversedMessagePuzzle or child is SimpleCalculationPuzzle or child is MemoryTestPuzzle or child is VampireArithmeticPuzzle or child is ShadowCodePuzzle or child is ThreeLeverssPuzzle or child is ThreeSistersPuzzle or child is RitualRhythmPuzzle:
			child.queue_free()

func show_simple_puzzle(puzzle_number: int):
	# Jednoduchý dialóg pre simuláciu hlavolamu (pre ostatné kapitoly)
	var puzzle_scene = AcceptDialog.new()
	add_child(puzzle_scene)

	var chapter_info = GameManager.chapter_info[chapter_number]
	var puzzle_name = chapter_info.puzzles[puzzle_number - 1]

	puzzle_scene.dialog_text = "Hlavolam: " + puzzle_name + "\n\nToto je simulácia hlavolamu.\nV skutočnej hre by tu bola interaktívna logika.\n\nChcete označiť tento hlavolam ako dokončený?"
	puzzle_scene.title = "Hlavolam " + str(puzzle_number)

	# Pridanie tlačidiel
	puzzle_scene.add_button("Dokončiť", true, "complete")
	puzzle_scene.add_button("Zrušiť", false, "cancel")

	puzzle_scene.custom_action.connect(_on_puzzle_action)
	puzzle_scene.popup_centered()

func _on_puzzle_action(action: String):
	if action == "complete":
		complete_puzzle(current_puzzle)
	current_puzzle = 0

func complete_puzzle(puzzle_number: int):
	# Označenie hlavolamu ako dokončený
	GameManager.complete_puzzle(chapter_number, puzzle_number)

	# Aktualizovanie UI
	update_puzzle_buttons()

	# Pre kapitoly 1 a 2 - špeciálne správanie
	if chapter_number == 1:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 2:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 3:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 4:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 5:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	elif chapter_number == 6:
		if puzzle_number == 1:
			# Po prvom hlavolame - už sa spustili interlude dialógy
			pass
		elif puzzle_number == 2:
			# Po druhom hlavolame - záverečné dialógy (koniec hry)
			if dialogue_system:
				var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
				dialogue_system.start_dialogue(success_dialogue)
	else:
		# Pre ostatné kapitoly - štandardné správanie
		if dialogue_system:
			var success_dialogue = dialogue_system.get_puzzle_success_dialogue(chapter_number, puzzle_number)
			dialogue_system.start_dialogue(success_dialogue)

	# Kontrola, či sú dokončené oba hlavolamy
	if GameManager.is_puzzle_completed(chapter_number, 1) and GameManager.is_puzzle_completed(chapter_number, 2):
		# Kapitola je dokončená - počkať na dokončenie záverečných dialógov
		if puzzle_number == 2 and (chapter_number == 1 or chapter_number == 2 or chapter_number == 3 or chapter_number == 4 or chapter_number == 5 or chapter_number == 6):
			# Pre kapitoly 1, 2, 3, 4, 5 a 6 - záverečné dialógy sa práve spustili, počkať na ich dokončenie
			if dialogue_system and not dialogue_system.dialogue_finished.is_connected(_on_final_dialogue_finished):
				dialogue_system.dialogue_finished.connect(_on_final_dialogue_finished, CONNECT_ONE_SHOT)
		else:
			# Pre ostatné kapitoly alebo prvý hlavolam - okamžite zobraziť dokončenie
			show_chapter_completion()

func show_chapter_completion():
	await get_tree().create_timer(2.0).timeout

	# Skontrolovať, či existuje ďalšia kapitola
	var next_chapter = chapter_number + 1
	var has_next_chapter = next_chapter <= 7 and GameManager.chapter_info.has(next_chapter)

	if has_next_chapter:
		# Automaticky prejsť na ďalšiu kapitolu
		print("✅ Kapitola ", chapter_number, " dokončená - automatický prechod na kapitolu ", next_chapter)

		# Krátke zobrazenie úspešného dokončenia
		var completion_dialog = AcceptDialog.new()
		add_child(completion_dialog)

		var dialog_text = "✅ Gratulujeme! Dokončili ste " + GameManager.chapter_info[chapter_number].title + "!"
		dialog_text += "\n\n🎯 Automaticky pokračujeme na " + GameManager.chapter_info[next_chapter].title + "..."

		completion_dialog.dialog_text = dialog_text
		completion_dialog.title = "Kapitola dokončená"
		completion_dialog.get_ok_button().text = "Pokračovať"
		completion_dialog.popup_centered()

		# Automatické zatvorenie po 3 sekundách
		await get_tree().create_timer(3.0).timeout
		completion_dialog.queue_free()

		# Automatický prechod na ďalšiu kapitolu
		GameManager.go_to_chapter(next_chapter)
	else:
		# Ak nie je ďalšia kapitola (kapitola 7 - epilóg), vrátiť sa do menu
		var completion_dialog = AcceptDialog.new()
		add_child(completion_dialog)
		completion_dialog.dialog_text = "🎉 Gratulujeme! Dokončili ste celú hru!\n\n" + GameManager.chapter_info[chapter_number].title + " bola posledná kapitola.\n\nĎakujeme za hranie 'Prekliate Dedičstvo'!"
		completion_dialog.title = "Hra dokončená"
		completion_dialog.get_ok_button().text = "Späť do menu"
		completion_dialog.popup_centered()

		completion_dialog.confirmed.connect(_on_return_to_menu)

func _on_final_dialogue_finished():
	# Záverečné dialógy skončili
	if chapter_number == 6:
		# Pre kapitolu 6 - automaticky prejsť na epilóg
		print("Kapitola 6 dokončená - automatický prechod na epilóg")
		await get_tree().create_timer(2.0).timeout
		GameManager.go_to_chapter(7)
	else:
		# Pre ostatné kapitoly - zobraziť dialóg dokončenia
		show_chapter_completion()

func _on_continue_to_next_chapter():
	# Pokračovať na ďalšiu kapitolu
	var next_chapter = chapter_number + 1
	GameManager.go_to_chapter(next_chapter)

func _on_return_to_menu():
	# Vrátiť sa do menu kapitol
	GameManager.go_to_chapters()

# Zachovať starú funkciu pre kompatibilitu
func _on_chapter_completed():
	_on_return_to_menu()

func _on_hint_pressed():
	if dialogue_system:
		var hint_dialogue = dialogue_system.get_puzzle_hint_dialogue(chapter_number, current_puzzle, 1)
		dialogue_system.start_dialogue(hint_dialogue)

func _on_start_pressed():
	# Pre kapitolu 7 - spustiť epilóg dialógy
	if chapter_number == 7:
		story_phase = 0
		if start_button:
			start_button.visible = false
		start_chapter_intro()

func start_chapter_music():
	"""Spustí príslušnú hudbu pre kapitolu"""
	match chapter_number:
		1:
			AudioManager.start_chapter_1()
		2:
			AudioManager.start_chapter_2()
		3:
			AudioManager.start_chapter_3()
		4:
			AudioManager.start_chapter_4()
		5:
			AudioManager.start_chapter_5()
		6:
			AudioManager.start_chapter_6()
		7:
			AudioManager.start_epilogue()
		_:
			# Fallback na library secrets
			AudioManager.play_music("library_secrets")

func return_from_puzzle():
	"""Univerzálna funkcia pre návrat z puzzle - obnoví hudbu"""
	AudioManager.return_from_puzzle()

func _on_back_pressed():
	GameManager.go_to_chapters()

func _input(event):
	if event.is_action_pressed("ui_cancel") and (not dialogue_system or not dialogue_system.visible):
		_on_back_pressed()
